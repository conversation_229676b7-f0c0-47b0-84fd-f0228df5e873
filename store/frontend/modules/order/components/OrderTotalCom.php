<?php

class OrderTotalCom extends CApplicationComponent
{
    /* modules
      [0] => ot_subtotal.php
      [1] => ot_shipping.php
      [2] => ot_tax.php
      [3] => ot_gv.php
      [4] => ot_coupon.php
      [5] => ot_gst.php
      [6] => ot_surcharge.php
      [7] => ot_total.php
     */

    public function __construct($_cart)
    {
        $ot_installed = 'ot_subtotal.php;ot_coupon.php;ot_gst.php;ot_gv.php;ot_surcharge.php;ot_total.php';
        if (!empty($ot_installed)) {
            $GLOBALS['cart'] = $_cart;
            if (isset($GLOBALS['cart']['checkout_info']['subtotal'])) {
                $GLOBALS['cart']['checkout_info']['total'] = $GLOBALS['cart']['checkout_info']['subtotal'];
            }

            $ot_module = explode(';', $ot_installed);

            foreach($ot_module as $val){
                $val = substr($val, 0, strrpos($val, '.'));
                $ot_class = str_replace(' ', '', ucwords(str_replace('_', ' ', $val)));
                $ot_class = 'Order' . $ot_class . 'Com';
                $GLOBALS['ot'][$val] = new $ot_class();
            }
        }
    }

    public function collectPosts()
    {
        $ot_data = array();

        $c_info = $GLOBALS['cart']['checkout_info'];

        if (isset(Yii::app()->currency->currencies[$c_info['currency']])) {
            while (list($class,) = each($GLOBALS['ot'])) {
                if ($GLOBALS['ot'][$class]->enabled && method_exists($GLOBALS['ot'][$class], 'collectPosts')) {
                    $_error = $GLOBALS['ot'][$class]->collectPosts();

                    if (!empty($_error)) {
                        $ot_data[] = array(
                            'code' => $GLOBALS['ot'][$class]->code,
                            'error' => $_error
                        );
                    }
                }
            }
        } else {
            $ot_data = array(
                'code' => 'ot',
                'error' => Yii::t('orderModule.OrderTotalCom', 'ERROR_INVALID_CURRENCY')
            );
        }

        return $ot_data;
    }

    public function process()
    {
        $ot_data = array();

        $c_info = $GLOBALS['cart']['checkout_info'];

        if (isset(Yii::app()->currency->currencies[$c_info['currency']])) {
            if ($c_info['currency_value'] != Yii::app()->currency->advanceCurrencyConversionRate($c_info['default_currency'],
                    $c_info['currency'], 'buy')) {
                $ot_data = array(
                    'code' => 'ot',
                    'error' => Yii::t('orderModule.OrderTotalCom', 'ERROR_CURRENCY_VALUE_MISMATCH')
                );
            } else {
                if (!empty($GLOBALS['ot'])) {
                    foreach ($GLOBALS['ot'] as $class => $obj) {
                        if ($GLOBALS['ot'][$class]->enabled && method_exists($GLOBALS['ot'][$class], 'process')) {
                            $GLOBALS['ot'][$class]->process();

                            for ($i = 0, $n = sizeof($GLOBALS['ot'][$class]->output); $i < $n; $i++) {
                                if (notNull($GLOBALS['ot'][$class]->output[$i]['title']) && notNull($GLOBALS['ot'][$class]->output[$i]['storage_text'])) {
                                    $ot_data[] = array(
                                        'code' => $GLOBALS['ot'][$class]->code,
                                        'title' => $GLOBALS['ot'][$class]->output[$i]['title'],
                                        'display_title' => $GLOBALS['ot'][$class]->output[$i]['display_title'],
                                        'checkout_value' => $GLOBALS['ot'][$class]->output[$i]['checkout_value'],
                                        'storage_text' => $GLOBALS['ot'][$class]->output[$i]['storage_text'],
                                        'storage_value' => $GLOBALS['ot'][$class]->output[$i]['storage_value'],
                                        'sort_order' => $GLOBALS['ot'][$class]->sort_order,
                                        'input' => $GLOBALS['ot'][$class]->output[$i]['input'],
                                        'output' => $GLOBALS['ot'][$class]->output[$i]['output']
                                    );
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $ot_data = array(
                'code' => 'ot',
                'error' => Yii::t('orderModule.OrderTotalCom', 'ERROR_INVALID_CURRENCY')
            );
        }

        return $ot_data;
    }

    public function applyCredit($order_id)
    {
        $store_credit_deducted = false;
        if (!empty($GLOBALS['ot'])) {
            foreach ($GLOBALS['ot'] as $class => $obj) {
                if ($GLOBALS['ot'][$class]->enabled && method_exists($GLOBALS['ot'][$class], 'applyCredit')) {
                    if ($class == 'ot_gv') {
                        $store_credit_deducted = $GLOBALS['ot'][$class]->applyCredit($order_id);
                    } else {
                        $GLOBALS['ot'][$class]->applyCredit($order_id);
                    }
                }
            }
        }
        return $store_credit_deducted;
    }

}