<?php

return array(
    "EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT" => "客户订单更新通知 #%d",
    "EMAIL_MAX_PENDING_ORDER_SUBJECT" => "Customer ID #%d Hit Maximum Pending Order",
    "EMAIL_MAX_PENDING_ORDER_TEXT" => "Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.",
    "EMAIL_PAYMENT_METHOD_VERIFICATION" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser. This link will confirm that you are the owner of this e-mail address and we will not ask for any {SYS_PAYMENT_METHOD} information in the page. Please understand that this step is taken to protect {SYS_PAYMENT_METHOD} account owners from unauthorized charges and this is a one-time process unless you change your {SYS_PAYMENT_METHOD} e-mail.<br />{SYS_VERIFICATION_LINK}<br /><br />If you have not made any purchase at {SYS_STORE_NAME}, please report this fraudulent use to {SYS_SUPPORT_EMAIL} immediately and we also advise you to report the case to {SYS_PAYMENT_METHOD} at their website and change your {SYS_PAYMENT_METHOD} password immediately. Thank you for shopping at {SYS_STORE_NAME}.<br /><br />",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />In order to protect the owner of the {SYS_PAYMENT_METHOD} account from fraudulent use, a verification e-mail with \"Subject: {SYS_MAIL_SUBJECT}\" has been sent to the e-mail address shown above. Please kindly check the e-mail account and follow the instructions stated to verify your {SYS_PAYMENT_METHOD} e-mail account. Thank you for helping us to serve you better. <br /><br />",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification Notice",
    "EMAIL_PAYPAL_PAYMENT_REVIEW" => "We have received a notification from PayPal that the payment for this order is currently being reviewed by PayPal, which typically will take up to 72 hours for clearance. Once the payment has cleared, we will proceed with your order accordingly and notify you via email.<br>If you would like to cancel the payment for this order, please kindly contact PayPal as we do not have the payment refund/cancellation option.<br>More information about PayPal <a href='https://www.paypal.com/c2/webapps/mpp/security/sell-paymentreview#psunderreview'>payment review</a><br>Thank you for your patience.",
    "EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT" => "PayPal Payment Review",
    "EMAIL_PAYPAL_PAYMENT_ECHECK" => "We would like to inform you that your order is pending for echeck clearance, which may typically take up to 8 days. We will notify you once we have received the notification from PayPal that your e-check is cleared.<br>If you do not wish for the echeck clearance, please do contact PayPal as there is no option for us to cancel this payment for you. We thank you for your patience and support.<br><a href='https://helpdesk.offgamers.com/support/solutions/articles/5000884295-how-do-i-avoid-sending-an-echeck-''>How do I avoid sending an echeck?</a>",
    "EMAIL_PAYPAL_PAYMENT_ECHECK_SUBJECT" => "PayPal Payment e-Check",
    "EMAIL_TEXT_BILLING_ADDRESS" => "账单地址",
    "EMAIL_TEXT_CLOSING" => "如果您要咨询或寻求帮助，请联系我们的在线客服平台或者发送您想咨询的问题到{SYS_SUPPORT_EMAIL}。 与我们联系时，请不要忘记附上您的订单号码，以方便我们尽快解决您的问题。 再次感谢您在{SYS_STORE_NAME}购物。",
    "EMAIL_TEXT_DATE_ORDERED" => "订购日期：",
    "EMAIL_TEXT_ORDER_NUMBER" => "订单号码：",
    "EMAIL_TEXT_ORDER_SUMMARY" => "订单摘要",
    'EMAIL_TEXT_ORDER_THANK_YOU' => '感谢您的购买{SUBTOTAL} at {STORE_NAME}。',
    "EMAIL_TEXT_PAYMENT_METHOD" => "付款方式",
    "EMAIL_TEXT_PRODUCTS" => "产品",
    "EMAIL_TEXT_SUBJECT" => "新订单 #%d",
    "EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION" => "自动",
    "EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION" => "手动",
    "EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT" => "订单 ID：%s 订单日期：%s 订单总额：%s 付款方式：%s 更新类型：%s 状态更新：%s -> %s 更新日期：%s 更新IP：%s 更新用户：%s 更新评论： %s",
    //
    "ERROR_INSUFFICIENT_PRODUCT_QUANTITY" => "Insufficient product quantity.",
    "ERROR_INVALID_ORDER" => "单子 #%d 不存在。",
    "ERROR_INVALID_ORDER_STATUS" => "Invalid Customer Order status.",
    "ERROR_INVALID_ORDER_STATUS_UPDATE" => "Invalid Customer Order status update from {SYS_STATUS_FROM} to {SYS_STATUS_TO}.",
    "ERROR_MAX_PENDING_ORDER" => "You have %d pending order, kindly check on previous orders' payment status.",
    'ERROR_ORDER_IN_PROGRESS' => '错误：您的订单可能已创建，请在当前订单中查询。如果下单失败，请您5分钟后重试。',
    'ERROR_MISSING_DTU_INFO' => '您尚未输入您的帐户信息。',
    'ERROR_MISSING_CART_ITEM' => '您尚未选择或添加任何商品进行结帐。',
    'ERROR_CONCURRENT_CHECKOUT' => '您有一个订单正在进行处理中，请您稍后再试。',
    'ERROR_CONFIRM_SC_PASSWORD' => '使用购物代金卷(SC)结账失败，您需要重新操作.',
    'TEXT_STOCK_NOT_AVAILABLE' => '要求购买的产品数量不足。查询到货，请联系 <a href=mailto:<EMAIL>><EMAIL></a>',
    'ERROR_PURCHASE_QUANTITY_EXCEEDED' => '您购买的产品数量已达到该产品的最大购买量。',
    
    //
    "TEXT_PAYMENT_METHOD_VERIFICATION_SENT" => "{SYS_PAYMENT_METHOD} verification e-mail sent.",
    "TEXT_PAYMENT_METHOD_VERIFICATION_NOTICE_SENT" => "{SYS_PAYMENT_METHOD} verification e-mail notice sent.",
    "LOG_SALES_ORDER" => "##{ORDER_ID}##: ##{SYS_STATUS_FROM}## ##{SYS_STATUS_TO}##",
);