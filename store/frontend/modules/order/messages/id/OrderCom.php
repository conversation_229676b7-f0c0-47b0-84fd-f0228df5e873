<?php

return array(
    "EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT" => "Notifikasi Pembaruan Order Pelanggan #%d",
    "EMAIL_MAX_PENDING_ORDER_SUBJECT" => "ID Pelanggan #%d mencapai order tersekat maksimum",
    "EMAIL_MAX_PENDING_ORDER_TEXT" => "ID Pelanggan #%d mencapai order tersekat maksimum, silakan hubungi pelanggan.",
    "EMAIL_PAYMENT_METHOD_VERIFICATION" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser. This link will confirm that you are the owner of this e-mail address and we will not ask for any {SYS_PAYMENT_METHOD} information in the page. Please understand that this step is taken to protect {SYS_PAYMENT_METHOD} account owners from unauthorized charges and this is a one-time process unless you change your {SYS_PAYMENT_METHOD} e-mail.<br />{SYS_VERIFICATION_LINK}<br /><br />If you have not made any purchase at {SYS_STORE_NAME}, please report this fraudulent use to {SYS_SUPPORT_EMAIL} immediately and we also advise you to report the case to {SYS_PAYMENT_METHOD} at their website and change your {SYS_PAYMENT_METHOD} password immediately. Thank you for shopping at {SYS_STORE_NAME}.<br /><br />", // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification", // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />In order to protect the owner of the {SYS_PAYMENT_METHOD} account from fraudulent use, a verification e-mail with \"Subject: {SYS_MAIL_SUBJECT}\" has been sent to the e-mail address shown above. Please kindly check the e-mail account and follow the instructions stated to verify your {SYS_PAYMENT_METHOD} e-mail account. Thank you for helping us to serve you better. <br /><br />", // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification Notice",
    "EMAIL_PAYPAL_PAYMENT_REVIEW" => "We have received a notification from PayPal that the payment for this order is currently being reviewed by PayPal, which typically will take up to 72 hours for clearance. Once the payment has cleared, we will proceed with your order accordingly and notify you via email.<br>If you would like to cancel the payment for this order, please kindly contact PayPal as we do not have the payment refund/cancellation option.<br>More information about PayPal <a href='https://www.paypal.com/c2/webapps/mpp/security/sell-paymentreview#psunderreview'>payment review</a><br>Thank you for your patience.",
    "EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT" => "PayPal Payment Review",
    "EMAIL_PAYPAL_PAYMENT_ECHECK" => "We would like to inform you that your order is pending for echeck clearance, which may typically take up to 8 days. We will notify you once we have received the notification from PayPal that your e-check is cleared.<br>If you do not wish for the echeck clearance, please do contact PayPal as there is no option for us to cancel this payment for you. We thank you for your patience and support.<br><a href='https://helpdesk.offgamers.com/support/solutions/articles/5000884295-how-do-i-avoid-sending-an-echeck-''>How do I avoid sending an echeck?</a>",
    "EMAIL_PAYPAL_PAYMENT_ECHECK_SUBJECT" => "PayPal Payment e-Check",
    "EMAIL_TEXT_BILLING_ADDRESS" => "Alamat Penagihan",
    "EMAIL_TEXT_CLOSING" => "Untuk  pertanyaan atau bantuan, Anda dapat menggunakan Online Live Support kami atau email pertanyaan ke {SYS_SUPPORT_EMAIL}. Harap ingat untuk menyertakan nomor order Anda ketika menghubungi kami untuk mempercepat proses. Terima kasih lagi untuk berbelanja di {SYS_STORE_NAME}.",
    "EMAIL_TEXT_DATE_ORDERED" => "Tanggal Order:",
    "EMAIL_TEXT_ORDER_NUMBER" => "Nomor Order:",
    "EMAIL_TEXT_ORDER_SUMMARY" => "Ringkasan Order",
    'EMAIL_TEXT_ORDER_THANK_YOU' => 'Terima kasih atas pembelian anda {SUBTOTAL} di {STORE_NAME}.',
    "EMAIL_TEXT_PAYMENT_METHOD" => "Metode Pembayaran",
    "EMAIL_TEXT_PRODUCTS" => "Produk",
    "EMAIL_TEXT_SUBJECT" => "Order Baru #%d",
    "EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION" => "Automatis",
    "EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION" => "Manual",
    "EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT" => "ID Order: %s Tanggal Order: %s Jumlah Order: %s Metode Pembayaran: %s Tipe Pembaruan: %s Pembaruan Status: %s -> %s Tanggal Pembaruan: %s IP Pembaruan: %s Penguna Pembaruan: %s Komentar Pembaruan: %s",
    //
    "ERROR_INSUFFICIENT_PRODUCT_QUANTITY" => "Kuantitas produk tidak memadai.",
    "ERROR_INVALID_ORDER" => "Order Pelanggan #%d tidak ada.",
    "ERROR_INVALID_ORDER_STATUS" => "Status order pelanggan tidak valid.",
    "ERROR_INVALID_ORDER_STATUS_UPDATE" => "Pembaruan Status order Pelanggan yang tidak valid dari {SYS_STATUS_FROM} ke {SYS_STATUS_TO}.",
    "ERROR_MAX_PENDING_ORDER" => "Anda memiliki %d order yang tersekat, silakan memeriksa status pembayaran pesanan sebelumnya.",
    'ERROR_ORDER_IN_PROGRESS' => 'Error telah terjadi. Order Anda mungkin telah dibuat. Mohon periksa Sejarah Order Sekarang untuk pesanan Anda terbaru. Anda dapat mencoba lagi dalam 5 menit jika pembelian tidak berhasil. Terima kasih.',
    'ERROR_MISSING_DTU_INFO' => 'You have not entered any account info.',
    'ERROR_MISSING_CART_ITEM' => 'You have not selected any items for checkout.',
    'ERROR_CONCURRENT_CHECKOUT' => 'You have another order in progress. Please try again.',
    'ERROR_CONFIRM_SC_PASSWORD' => 'Your session has expired. Please try again.',
    'TEXT_STOCK_NOT_AVAILABLE' => 'Jumlah yang diminta tidak tersedia. Untuk memeriksa ketersediaan, silakan hubungi <a href=mailto:<EMAIL>><EMAIL></a>',
    'ERROR_PURCHASE_QUANTITY_EXCEEDED' => 'Anda telah melebihi batas pembelian yang ditetapkan untuk produk ini.',
    
    //
    "TEXT_PAYMENT_METHOD_VERIFICATION_SENT" => "Email verifikasi{SYS_PAYMENT_METHOD} telah di-kirim.",
    "TEXT_PAYMENT_METHOD_VERIFICATION_NOTICE_SENT" => "Notis email verifikasi{SYS_PAYMENT_METHOD} telah di-kirim.",
    "LOG_SALES_ORDER" => "##{ORDER_ID}##: ##{SYS_STATUS_FROM}## ##{SYS_STATUS_TO}##",
);