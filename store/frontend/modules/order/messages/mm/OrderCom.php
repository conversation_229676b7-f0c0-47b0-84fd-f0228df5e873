<?php

return array(
    "EMAIL_CUSTOMER_ORDER_UPDATE_NOTIFICATION_SUBJECT" => "Customer Order Update Notification #%d",
    "EMAIL_MAX_PENDING_ORDER_SUBJECT" => "Customer ID #%d Hit Maximum Pending Order",
    "EMAIL_MAX_PENDING_ORDER_TEXT" => "Customer ID #%d Hit Maximum Pending Order, kindly liaise with customer.",
    "EMAIL_PAYMENT_METHOD_VERIFICATION" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />Please kindly verify this e-mail account by clicking on the following link or copy and paste the link to your browser. This link will confirm that you are the owner of this e-mail address and we will not ask for any {SYS_PAYMENT_METHOD} information in the page. Please understand that this step is taken to protect {SYS_PAYMENT_METHOD} account owners from unauthorized charges and this is a one-time process unless you change your {SYS_PAYMENT_METHOD} e-mail.<br />{SYS_VERIFICATION_LINK}<br /><br />If you have not made any purchase at {SYS_STORE_NAME}, please report this fraudulent use to {SYS_SUPPORT_EMAIL} immediately and we also advise you to report the case to {SYS_PAYMENT_METHOD} at their website and change your {SYS_PAYMENT_METHOD} password immediately. Thank you for shopping at {SYS_STORE_NAME}.<br /><br />",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE" => "You have made a {SYS_PAYMENT_METHOD} payment of {SYS_ORDER_AMOUNT} to {SYS_STORE_NAME} using the following account: <br /><br />  {SYS_CUSTOMER_EMAIL} <br /><br />In order to protect the owner of the {SYS_PAYMENT_METHOD} account from fraudulent use, a verification e-mail with \"Subject: {SYS_MAIL_SUBJECT}\" has been sent to the e-mail address shown above. Please kindly check the e-mail account and follow the instructions stated to verify your {SYS_PAYMENT_METHOD} e-mail account. Thank you for helping us to serve you better. <br /><br />",  // EN
    "EMAIL_PAYMENT_METHOD_VERIFICATION_NOTICE_SUBJECT" => "{SYS_PAYMENT_METHOD} Account Verification Notice",
    "EMAIL_PAYPAL_PAYMENT_REVIEW" => "We have received a notification from PayPal that the payment for this order is currently being reviewed by PayPal, which typically will take up to 72 hours for clearance. Once the payment has cleared, we will proceed with your order accordingly and notify you via email.<br>If you would like to cancel the payment for this order, please kindly contact PayPal as we do not have the payment refund/cancellation option.<br>More information about PayPal <a href='https://www.paypal.com/c2/webapps/mpp/security/sell-paymentreview#psunderreview'>payment review</a><br>Thank you for your patience.",
    "EMAIL_PAYPAL_PAYMENT_REVIEW_SUBJECT" => "PayPal Payment Review",
    "EMAIL_PAYPAL_PAYMENT_ECHECK" => "We would like to inform you that your order is pending for echeck clearance, which may typically take up to 8 days. We will notify you once we have received the notification from PayPal that your e-check is cleared.<br>If you do not wish for the echeck clearance, please do contact PayPal as there is no option for us to cancel this payment for you. We thank you for your patience and support.<br><a href='https://helpdesk.offgamers.com/support/solutions/articles/5000884295-how-do-i-avoid-sending-an-echeck-''>How do I avoid sending an echeck?</a>",
    "EMAIL_PAYPAL_PAYMENT_ECHECK_SUBJECT" => "PayPal Payment e-Check",
    "EMAIL_TEXT_BILLING_ADDRESS" => "Billing Address",
    "EMAIL_TEXT_CLOSING" => "Thank you for all your support! For any enquiries or assistance, you may e-mail your enquiries to {SYS_SUPPORT_EMAIL}. Please remember to include your Order Number when contacting us to expedite the process. Thanks again for shopping at {SYS_STORE_NAME}.",
    "EMAIL_TEXT_DATE_ORDERED" => "Order Date:",
    "EMAIL_TEXT_ORDER_NUMBER" => "မှာယူမှုနံပါတ်:",
    "EMAIL_TEXT_ORDER_SUMMARY" => "မှာယူမှုအနှစ်ချုပ်-",
    'EMAIL_TEXT_ORDER_THANK_YOU' => '{STORE_NAME} တွင် {SUBTOTAL} ဝယ်ယူသည့်အတွက် သင့်ကို ကျေးဇူးတင်ပါသည်။',
    "EMAIL_TEXT_PAYMENT_METHOD" => "ငွေချေနည်းလမ်း",
    "EMAIL_TEXT_PRODUCTS" => "ကုန်ပစ္စည်း",
    "EMAIL_TEXT_SUBJECT" => "New Order #%d",
    "EMAIL_TEXT_TRANS_UPDATE_AUTO_NOTIFICATION" => "Automatic",
    "EMAIL_TEXT_TRANS_UPDATE_MANUAL_NOTIFICATION" => "Manual",
    "EMAIL_TRANS_UPDATE_NOTIFICATION_CONTENT" => "Order ID: %s Order Date: %s Order Amount: %s Payment Method: %s Update Type: %s Status Update: %s -> %s Update Date: %s Update IP: %s Update User: %s Update Comment: %s",
    //
    "ERROR_INSUFFICIENT_PRODUCT_QUANTITY" => "Insufficient product quantity.",
    "ERROR_INVALID_ORDER" => "Customer Order #%d does not existing.",
    "ERROR_INVALID_ORDER_STATUS" => "Invalid Customer Order status.",
    "ERROR_INVALID_ORDER_STATUS_UPDATE" => "Invalid Customer Order status update from {SYS_STATUS_FROM} to {SYS_STATUS_TO}.",
    "ERROR_MAX_PENDING_ORDER" => "You have %d pending order, kindly check on previous orders' payment status.",
    'ERROR_ORDER_IN_PROGRESS' => 'ချွတ်ယွင်းချက်တစ်ခု ဖြစ်သွားသည်။ သင့်မှာယူမှုကို ပြုလုပ်လိုက်ပြီး ဖြစ်နိုင်ပါသည်။ သင်၏ နောက်ဆုံးမှာယူမှုအတွက် သင်၏မှာယူမှုမှတ်တမ်းကို စစ်ဆေးပေးပါ။ ဝယ်ယူမှုမအောင်မြင်ပါက 5 မိနစ်အတွင်း သင်ထပ်မံကြိုးစားနိုင်ပါသည်။ ကျေးဇူးတင်ပါသည်။',
    'ERROR_MISSING_DTU_INFO' => 'မည်သည့်အကောင့်အချက်အလက်ကိုမျှ သင် ရိုက်ထည့်မထားပါ။',
    'ERROR_MISSING_CART_ITEM' => 'ငွေချေရန် သင် မည်သည့်ပစ္စည်းကိုမျှ ရွေးချယ်မထားပါ။',
    'ERROR_CONCURRENT_CHECKOUT' => 'You have another order in progress. Please try again.',
    'ERROR_CONFIRM_SC_PASSWORD' => 'သင်၏ ဝင်ရောက်ချိန် သက်တမ်းကုန်သွားပါပြီ။ ထပ်မံကြိုးစားပါ။',
    'TEXT_STOCK_NOT_AVAILABLE' => 'တောင်းဆိုထားသောအရေအတွက်ကို မရနိုင်ပါ။ မည်မျှရရှိနိုင်သလဲ သိရှိရန် <a href=mailto:<EMAIL>><EMAIL></a> ကို ဆက်သွယ်ပေးပါ',
    'ERROR_PURCHASE_QUANTITY_EXCEEDED' => 'ဤထုတ်ကုန်အတွက် သတ်မှတ်ထားသော ဝယ်ယူမှုကန့်သတ်ချက်ကို သင် ကျော်လွန်သွားပါပြီ။',
    
    //
    "TEXT_PAYMENT_METHOD_VERIFICATION_SENT" => "{SYS_PAYMENT_METHOD} verification e-mail sent.",
    "TEXT_PAYMENT_METHOD_VERIFICATION_NOTICE_SENT" => "{SYS_PAYMENT_METHOD} verification e-mail notice sent.",
    "LOG_SALES_ORDER" => "##{ORDER_ID}##: ##{SYS_STATUS_FROM}## ##{SYS_STATUS_TO}##",
);