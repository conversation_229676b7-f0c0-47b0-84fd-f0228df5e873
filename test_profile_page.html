<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Page Test - E-Invoice Type</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="account/frontend/media/css/main.css">
    <link rel="stylesheet" href="account/frontend/media/css/layout.css">
</head>

<body>
    <div class="page-title">
        <h1>Profile Page Test - E-Invoice Type</h1>
    </div>

    <div class="card">
        
        <div class="einvoice-type-wrapper">
            <div class="einvoice-type-title">
                <h2>E-Invoice Type</h2>
                <p>This section allows you to select your e-invoice type for billing purposes.</p>
            </div>
            
            <div class="einvoice-type-toggle">
                <div class="einvoice-toggle-container">
                    <div id="toggle-personal" class="einvoice-toggle-button active">Personal</div>
                    <div id="toggle-business" class="einvoice-toggle-button">Business</div>
                    <div class="einvoice-toggle-slider"></div>
                </div>
                <input type="hidden" id="einvoice-type-input" name="einvoice-type-input" value="personal">
            </div>
        </div>        

        <div id="business-section" style="display: none;">
            <!-- Partition -->
            <div class="partition"></div>

            <!-- Business Section -->
            <div class="card-tab">
                <h2>
                    COMPANY DETAILS
                </h2>
            </div>
            <div class="form-normal">
                <label>
                    Company Name
                </label>
                <input type="text" placeholder="Company Name">
                <label>
                    Company Registration Number
                </label>
                <input type="text" placeholder="Company Registration Number">
                <label>
                    Tax Identification Number
                </label>
                <div class="input-with-dropdown">
                    <label>Tax registration number</label>
                    <div class="input-group">
                        <select class="country-select">
                            <option value="ES" selected>ES</option>
                            <option value="MY">MY</option>
                            <option value="US">US</option>
                            <!-- Add more countries as needed -->
                        </select>
                        <input type="text" placeholder="Tax registration number">
                    </div>
                </div>
                
                <style>
                    .input-with-dropdown {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        max-width: 400px;
                        margin: 20px 0;
                    }
                
                    .input-with-dropdown label {
                        display: block;
                        margin-bottom: 8px;
                        font-size: 14px;
                        color: #333;
                        font-weight: 500;
                    }
                
                    .input-group {
                        position: relative;
                        display: flex;
                        width: 100%;
                    }
                
                    .country-select {
                        width: 80px;
                        padding: 12px 15px;
                        padding-right: 30px;
                        /* Space for dropdown icon */
                        border: 1px solid #ddd;
                        border-right: none;
                        border-radius: 4px 0 0 4px;
                        background-color: #fff;
                        color: #333;
                        font-size: 14px;
                        -webkit-appearance: none;
                        -moz-appearance: none;
                        appearance: none;
                        cursor: pointer;
                    }
                
                    .input-group input {
                        flex: 1;
                        padding: 12px 15px;
                        border: 1px solid #ddd;
                        border-radius: 0 4px 4px 0;
                        font-size: 14px;
                        color: #333;
                        outline: none;
                    }
                
                    .input-group input::placeholder {
                        color: #999;
                    }
                
                    .dropdown-icon {
                        position: absolute;
                        left: 55px;
                        /* Position near end of select */
                        top: 50%;
                        transform: translateY(-50%);
                        pointer-events: none;
                        color: #666;
                        font-size: 12px;
                    }
                
                    /* Focus states */
                    .country-select:focus,
                    .input-group input:focus {
                        border-color: #4a90e2;
                        outline: none;
                        box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
                    }
                </style>
            </div>
        </div>
        <!-- Partition -->
        <div class="partition"></div>

        <!-- Personal Info -->
        <div class="card-tab">
            <h2>
                PERSONAL
            </h2>
        </div>
        <div class="form-normal">
            <div id="customer-name">
                <div class="input-row-2-coll">
                    <label>
                        NAME
                    </label>
                    <input type="text" placeholder="First Name">
                </div>
                <div class="input-row-2-coll">
                    <div class="input-row-2-coll-right">
                        <input type="text" placeholder="Last Name">
                    </div>
                </div>
            </div>
            
            <label>
                GENDER
            </label>
            <input type="radio" name="gender" value="male"> Male
            <input type="radio" name="gender" value="female"> Female
            <label>
                DATE OF BIRTH
            </label>
            <input type="date" name="dob">
            <form id="ic-form">
                <label>
                    National Identity Number
                </label>
                <div class="nid-input-group">
                    <input type="tel" class="nid-input-field" maxlength="6" placeholder="XXXXXX" id="nric-1">
                    <span class="nid-separator">-</span>
                    <input type="tel" class="nid-input-field" maxlength="2" placeholder="XX" id="nric-2">
                    <span class="nid-separator">-</span>
                    <input type="tel" class="nid-input-field" maxlength="4" placeholder="XXXX" id="nric-3">
                </div>
                <div id="national-identity-error" class="input-row-error" style="display: none;">
                    <i class="fa fa-exclamation-circle"></i> <span class="help-block"></span>
                </div>
                <button type="submit">Submit</button>
            </form>
            <div class="tax-input">
                <select>
                    <option value="ES" selected="ES" disabled>ES</option>
                </select>
                <input type="text" placeholder="Tax registration number">
            </div>
            <div class="form-group">
                <label class="control-label" for="customers-customers_tin">Tax identification number (TIN)</label>
                <div class="input-group">
                    <span class="input-group-addon">MY</span>
                    <input type="text" id="customers-customers_tin" class="form-control" name="Customers[customers_tin]" autocomplete="off">
                </div>
                <div class="help-block"></div>
            </div>
        </div>

        <!-- Partition -->
        <div class="partition"></div>
        
        <!-- Billing Address -->
        <div class="card-tab">
            <h2 id="billing-address-title">
                BILLING ADDRESS
            </h2>
        </div>
        <div class="form-normal">
            <label>
                ADDRESS
            </label>
            <input type="text" placeholder="Address">
            <input type="text" placeholder="Address 2">
            <label>
                CITY
            </label>
            <input type="text" placeholder="City">
            <label for="Country">
                COUNTRY
            </label>
            <input type="text" placeholder="Country">
            <div class="input-row-2-coll">
                <label>
                    ZIP
                </label>
                <input type="text" placeholder="Zip">
            </div>
            <div class="input-row-2-coll">
                <div class="input-row-2-coll-right">
                    <input type="text" placeholder="State">
                </div>
            </div>
            <div class="clearfix">
                <div class="input-row"></div>
                <div class="btn-spot">
                    <button type="submit" class="btn-submit">
                        SAVE CHANGED
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
<script>
    document.addEventListener('DOMContentLoaded', () => {        
        const toggleButtons = document.querySelectorAll('.einvoice-toggle-button');
        const typeInput = document.getElementById('einvoice-type-input');
        const slider = document.querySelector('.einvoice-toggle-slider');
        const businessSection = document.getElementById('business-section');
        const billingAddressTitle = document.getElementById('billing-address-title');
        const errorDiv = document.getElementById('national-identity-error');
        billingAddressTitle.textContent = 'BILLING ADDRESS';

        // Toggle button click handler
        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const type = button.id.replace('toggle-', '');
                typeInput.value = type;

                // Update active styles
                toggleButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                console.log(type);

                // Update slider position
                if (slider) {
                    slider.style.transform = type === 'personal' ? 'translateX(0)' : 'translateX(100%)';
                }

                // Show/hide business section
                if (type === 'business') {
                    businessSection.style.display = 'block';
                    billingAddressTitle.textContent = 'COMPANY ADDRESS';
                } else {
                    businessSection.style.display = 'none';
                    billingAddressTitle.textContent = 'BILLING ADDRESS';
                }
            });
        });

        // National identity input auto-tab
        const nidFields = document.querySelectorAll('.nid-input-field');
        nidFields.forEach((field, index) => {
            field.addEventListener('input', () => {
                field.value = field.value.replace(/\D/g, '');
                const maxLength = parseInt(field.getAttribute('maxlength'));
                if (field.value.length >= maxLength) {
                    const nextField = nidFields[index + 1];
                    if (nextField) nextField.focus();
                }
            });

            field.addEventListener('keydown', (e) => {
                if (e.key === 'Backspace' && field.value.length === 0) {
                    const prevField = nidFields[index - 1];
                    if (prevField) prevField.focus();
                }
            });
        });

        // Form submission handling
        const form = document.querySelector('form');
        form.addEventListener('submit', (e) => {
            e.preventDefault(); // Prevent default form submission

            // Concatenate input fields to form the full IC number
            const icNumber = Array.from(nidFields)
                .map(field => field.value)
                .join('');

            // Validate the IC number
            if (isValidIC(icNumber)) {
                errorDiv.style.display = 'none';
            } else {
                errorDiv.style.display = 'block';
                errorDiv.textContent = 'Invalid Malaysian IC number';
            }
        });

        function isValidIC(ic) {
            // Must be exactly 12 digits
            if (!/^\d{12}$/.test(ic)) return false;

            const dob = ic.substring(0, 6);
            const stateCode = ic.substring(6, 8);

            // Check date
            const year = parseInt(dob.substring(0, 2), 10);
            const month = parseInt(dob.substring(2, 4), 10);
            const day = parseInt(dob.substring(4, 6), 10);

            const currentYear = new Date().getFullYear();
            const fullYear = year <= currentYear % 100
                ? currentYear - (currentYear % 100) + year
                : 1900 + year;

            const date = new Date(fullYear, month - 1, day);

            if (
                date.getFullYear() !== fullYear ||
                date.getMonth() !== (month - 1) ||
                date.getDate() !== day
            ) {
                return false;
            }

            // Check state code
            const validStates = [
                ...Array.from({ length: 16 }, (_, i) => String(i + 1).padStart(2, '0')),  // "01" to "16"
                ...Array.from({ length: 26 }, (_, i) => String(i + 60))                   // "60" to "85"
            ];

            if (!validStates.includes(stateCode)) return false;

            return true;
        }

    });

</script>
</html>