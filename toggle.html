<!DOCTYPE html>
<html>
<head>
  <style>
    .toggle-container {
      display: flex;
      width: 200px;
      height: 50px;
      background: #e0e0e0;
      border-radius: 25px;
      overflow: hidden;
      position: relative;
      padding: 2px; /* Space between buttons and container */
    }

    .toggle-button {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 1;
      transition: color 0.3s;
      margin: 0 2px; /* Space between buttons */
    }

    .toggle-slider {
      position: absolute;
      width: 48%; /* Slightly smaller to leave space */
      height: 88%; /* Slightly smaller to leave space */
      background: #fff;
      border-radius: 25px; /* Adjusted to match smaller size */
      transition: transform 0.3s ease;
      z-index: 0;
      top: 5%; /* Center vertically with smaller height */
      left: 2px; /* Align with padding */
    }

    #personal.active ~ .toggle-slider {
      transform: translateX(0);
    }

    #business.active ~ .toggle-slider {
      transform: translateX(100%);
    }

    .toggle-button.active {
      color: #3BB3FF;
    }

    .toggle-button:not(.active) {
      color: #000;
    }
  </style>
</head>
<body>
  <div class="toggle-container">
    <div id="personal" class="toggle-button active">Personal</div>
    <div id="business" class="toggle-button">Business</div>
    <div class="toggle-slider"></div>
  </div>

  <script>
    const buttons = document.querySelectorAll('.toggle-button');
    buttons.forEach(button => {
      button.addEventListener('click', () => {
        buttons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
      });
    });
  </script>
</body>
</html>